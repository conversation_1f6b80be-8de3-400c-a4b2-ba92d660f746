%% 第四题：异构网络资源管理优化 - 整合版本
% 作者：AI Assistant
% 日期：2025-08-10
% 描述：将所有功能整合到一个MATLAB文件中，实现异构网络资源管理优化

clear; clc; close all;

fprintf('========================================\n');
fprintf('    第四题：异构网络资源管理优化\n');
fprintf('    整合版本 - 所有功能集成\n');
fprintf('========================================\n\n');

try
    %% ===== 第一部分：数据读取和预处理 =====
    fprintf('=== 第一步：数据读取和预处理 ===\n');
    
    % 数据路径
    data_path = '附件/附件4/';
    
    % 1. 读取用户任务到达情况
    fprintf('正在读取用户任务到达数据...\n');
    taskflow_data = readtable([data_path, 'taskflow.xlsx']);
    fprintf('任务到达数据读取成功，数据大小: %d行 x %d列\n', size(taskflow_data));
    
    % 2. 读取宏基站信道数据
    fprintf('正在读取宏基站(MBS_1)信道数据...\n');
    mbs_data = readtable([data_path, 'MBS_1.xlsx']);
    fprintf('宏基站信道数据读取成功，数据大小: %d行 x %d列\n', size(mbs_data));
    
    % 3. 读取小基站信道数据
    fprintf('正在读取小基站信道数据...\n');
    sbs_data = cell(3, 1);
    sbs_names = {'SBS_1', 'SBS_2', 'SBS_3'};
    
    for i = 1:3
        sbs_data{i} = readtable([data_path, sbs_names{i}, '.xlsx']);
        fprintf('%s信道数据读取成功，数据大小: %d行 x %d列\n', ...
                sbs_names{i}, size(sbs_data{i}));
    end
    
    % 4. 基站位置信息
    bs_positions = struct();
    bs_positions.MBS_1 = [0, 0];
    bs_positions.SBS_1 = [0, 500];
    bs_positions.SBS_2 = [-433.0127, -250];
    bs_positions.SBS_3 = [433.0127, -250];
    
    % 5. 用户类型信息
    user_types = struct();
    user_types.URLLC = {'U1', 'U2', 'U3', 'U4', 'U5', 'U6', 'U7', 'U8', 'U9', 'U10'};
    user_types.eMBB = {'e1', 'e2', 'e3', 'e4', 'e5', 'e6', 'e7', 'e8', 'e9', 'e10', ...
                       'e11', 'e12', 'e13', 'e14', 'e15', 'e16', 'e17', 'e18', 'e19', 'e20'};
    user_types.mMTC = {'m1', 'm2', 'm3', 'm4', 'm5', 'm6', 'm7', 'm8', 'm9', 'm10', ...
                       'm11', 'm12', 'm13', 'm14', 'm15', 'm16', 'm17', 'm18', 'm19', 'm20', ...
                       'm21', 'm22', 'm23', 'm24', 'm25', 'm26', 'm27', 'm28', 'm29', 'm30', ...
                       'm31', 'm32', 'm33', 'm34', 'm35', 'm36', 'm37', 'm38', 'm39', 'm40'};
    
    fprintf('数据读取完成！\n');
    
    %% ===== 第二部分：系统参数设置 =====
    fprintf('\n=== 第二步：系统参数设置 ===\n');
    
    % 切片参数
    slice_params = struct();
    slice_params.URLLC = struct('rb_per_user', 10, 'rate_sla', 10e6, 'delay_sla', 5e-3, 'penalty', 5);
    slice_params.eMBB = struct('rb_per_user', 5, 'rate_sla', 50e6, 'delay_sla', 100e-3, 'penalty', 3);
    slice_params.mMTC = struct('rb_per_user', 2, 'rate_sla', 1e6, 'delay_sla', 500e-3, 'penalty', 1);
    
    % 基站参数
    bs_params = struct();
    bs_params.MBS_1 = struct('rb_total', 100, 'power_min', 10, 'power_max', 40, 'pos', [0, 0]);
    bs_params.SBS_1 = struct('rb_total', 50, 'power_min', 10, 'power_max', 30, 'pos', [0, 500]);
    bs_params.SBS_2 = struct('rb_total', 50, 'power_min', 10, 'power_max', 30, 'pos', [-433.0127, -250]);
    bs_params.SBS_3 = struct('rb_total', 50, 'power_min', 10, 'power_max', 30, 'pos', [433.0127, -250]);
    
    % 物理层参数
    phy_params = struct();
    phy_params.bandwidth = 360e3;  
    phy_params.time_slot = 1e-3;   
    phy_params.noise_spectral_density = -174;  
    phy_params.noise_figure = 7;  
    phy_params.alpha = 0.95;       
    
    fprintf('系统参数设置完成\n');
    
    %% ===== 第三部分：数据预处理 =====
    fprintf('\n=== 第三步：数据预处理 ===\n');
    
    % 构建信道矩阵
    [channel_matrix, user_positions] = build_channel_matrix_integrated(mbs_data, sbs_data, user_types, bs_positions);
    
    % 构建任务矩阵
    task_matrix = build_task_matrix_integrated(taskflow_data, user_types);
    
    fprintf('数据预处理完成\n');
    
    %% ===== 第四部分：迭代优化求解 =====
    fprintf('\n=== 第四步：开始迭代优化求解 ===\n');
    
    % 优化参数
    max_iterations = 50;
    convergence_threshold = 1e-6;
    
    % 初始化结果结构
    results = struct();
    results.user_assignment = zeros(70, 4);  
    results.slice_allocation = zeros(4, 3);  
    results.power_allocation = zeros(4, 1);  
    results.qos_history = zeros(max_iterations, 1);
    
    % 迭代优化
    for iter = 1:max_iterations
        fprintf('迭代 %d/%d\n', iter, max_iterations);
        
        % 用户接入决策优化
        if iter == 1
            % 初始用户接入决策
            results.user_assignment = initial_user_assignment_integrated(channel_matrix, user_types);
        else
            % 优化用户接入决策
            results.user_assignment = optimize_user_assignment_integrated(channel_matrix, user_types, ...
                results.slice_allocation, results.power_allocation, slice_params, phy_params);
        end
        
        % 切片资源分配优化
        results.slice_allocation = optimize_slice_allocation_integrated(results.user_assignment, ...
            user_types, bs_params, slice_params);
        
        % 功率分配优化
        results.power_allocation = optimize_power_allocation_integrated(results.user_assignment, ...
            results.slice_allocation, channel_matrix, user_types, bs_params, slice_params, phy_params);
        
        % 计算总QoS
        total_qos = calculate_total_qos_integrated(results.user_assignment, results.slice_allocation, ...
            results.power_allocation, channel_matrix, user_types, slice_params, phy_params);
        
        results.qos_history(iter) = total_qos;
        
        % 检查收敛
        if iter > 1 && abs(results.qos_history(iter) - results.qos_history(iter-1)) < convergence_threshold
            fprintf('算法收敛，迭代次数: %d\n', iter);
            break;
        end
    end
    
    %% ===== 第五部分：结果分析和输出 =====
    fprintf('\n=== 第五步：结果分析和输出 ===\n');
    
    % 计算最终QoS
    final_qos = calculate_total_qos_integrated(results.user_assignment, results.slice_allocation, ...
        results.power_allocation, channel_matrix, user_types, slice_params, phy_params);
    
    fprintf('最终总QoS: %.4f\n', final_qos);
    
    % 输出详细结果
    output_user_assignment_integrated(results.user_assignment, user_types);
    output_slice_allocation_integrated(results.slice_allocation, bs_params);
    output_power_allocation_integrated(results.power_allocation, bs_params);
    
    % 绘制收敛曲线
    plot_convergence_integrated(results.qos_history(1:iter));
    
    % 保存结果
    save('problem4_results.mat', 'results', 'final_qos', 'slice_params', 'bs_params', 'phy_params');
    
    fprintf('\n========================================\n');
    fprintf('    求解完成！结果已保存\n');
    fprintf('========================================\n');
    
catch ME
    fprintf('\n错误：%s\n', ME.message);
    fprintf('请检查数据文件是否存在，并确保所有函数都正确定义。\n');
end

%% ===== 子函数定义部分 =====
% 以下是所有子函数的定义，整合了原来分离的各个.m文件的功能

function [channel_matrix, user_positions] = build_channel_matrix_integrated(mbs_data, sbs_data, user_types, bs_positions)
%% 构建用户-基站信道矩阵

% 初始化
num_users = 70;  % 总用户数
num_bs = 4;      % 总基站数
channel_matrix = zeros(num_users, num_bs);

% 构建用户列表
all_users = [user_types.URLLC, user_types.eMBB, user_types.mMTC];

% 基站名称列表
bs_names = {'MBS_1', 'SBS_1', 'SBS_2', 'SBS_3'};

% 构建信道矩阵
for i = 1:num_users
    user_name = all_users{i};

    for j = 1:num_bs
        bs_name = bs_names{j};

        % 根据基站类型选择数据源
        if strcmp(bs_name, 'MBS_1')
            data_source = mbs_data;
        else
            sbs_idx = str2double(bs_name(5));  % 提取SBS编号
            data_source = sbs_data{sbs_idx};
        end

        % 查找用户在该基站的信道数据
        if ismember(user_name, data_source.Properties.VariableNames)
            channel_matrix(i, j) = data_source.(user_name)(1);  % 取第一个时间点的数据
        else
            % 如果数据中没有该用户，使用距离模型估算
            user_pos = estimate_user_position_integrated(user_name, user_types);
            bs_pos = bs_positions.(bs_name);
            distance = norm(user_pos - bs_pos);
            channel_matrix(i, j) = calculate_path_loss_integrated(distance);
        end
    end
end

% 估算用户位置
user_positions = struct();
for i = 1:num_users
    user_name = all_users{i};
    user_positions.(user_name) = estimate_user_position_integrated(user_name, user_types);
end

end

function user_pos = estimate_user_position_integrated(user_name, user_types)
%% 估算用户位置

if startsWith(user_name, 'U')  % URLLC用户
    idx = str2double(user_name(2:end));
    angle = (idx - 1) * 2 * pi / 10;  % 均匀分布在圆周上
    radius = 200 + rand() * 100;      % 200-300m范围内
    user_pos = [radius * cos(angle), radius * sin(angle)];

elseif startsWith(user_name, 'e')  % eMBB用户
    idx = str2double(user_name(2:end));
    angle = (idx - 1) * 2 * pi / 20;  % 均匀分布在圆周上
    radius = 150 + rand() * 150;      % 150-300m范围内
    user_pos = [radius * cos(angle), radius * sin(angle)];

elseif startsWith(user_name, 'm')  % mMTC用户
    idx = str2double(user_name(2:end));
    angle = (idx - 1) * 2 * pi / 40;  % 均匀分布在圆周上
    radius = 100 + rand() * 200;      % 100-300m范围内
    user_pos = [radius * cos(angle), radius * sin(angle)];
end

end

function path_loss = calculate_path_loss_integrated(distance)
%% 计算路径损耗

% 基础路径损耗 (dB)
base_loss = 128.1 + 37.6 * log10(distance/1000);  % 距离单位为m

% 阴影衰落 (dB)
shadow_fading = randn() * 8;  % 标准差8dB

% 总路径损耗
path_loss = base_loss + shadow_fading;

end

function task_matrix = build_task_matrix_integrated(taskflow_data, user_types)
%% 构建任务到达矩阵

% 获取时间列名
if ismember('Time', taskflow_data.Properties.VariableNames)
    time_col = 'Time';
elseif ismember('时间', taskflow_data.Properties.VariableNames)
    time_col = '时间';
else
    time_col = taskflow_data.Properties.VariableNames{1};
end

% 获取时间点数
num_time_slots = height(taskflow_data);

% 构建用户列表
all_users = [user_types.URLLC, user_types.eMBB, user_types.mMTC];
num_users = length(all_users);

% 初始化任务矩阵
task_matrix = zeros(num_users, num_time_slots);

% 填充任务矩阵
for i = 1:num_users
    user_name = all_users{i};

    % 检查该用户是否在任务数据中
    if ismember(user_name, taskflow_data.Properties.VariableNames)
        task_matrix(i, :) = taskflow_data.(user_name);
    else
        % 如果数据中没有该用户，根据用户类型生成任务
        task_matrix(i, :) = generate_task_sequence_integrated(user_name, num_time_slots);
    end
end

end

function task_seq = generate_task_sequence_integrated(user_name, num_time_slots)
%% 根据用户类型生成任务序列

if startsWith(user_name, 'U')  % URLLC用户 - 泊松分布
    lambda = 0.3;  % 平均到达率
    task_seq = poissrnd(lambda, 1, num_time_slots);

elseif startsWith(user_name, 'e')  % eMBB用户 - 均匀分布
    task_seq = randi([0, 1], 1, num_time_slots);

elseif startsWith(user_name, 'm')  % mMTC用户 - 均匀分布
    task_seq = randi([0, 1], 1, num_time_slots);
end

end

function user_assignment = initial_user_assignment_integrated(channel_matrix, user_types)
%% 初始用户接入决策 - 基于信道质量

% 初始化
num_users = size(channel_matrix, 1);
num_bs = size(channel_matrix, 2);
user_assignment = zeros(num_users, num_bs);

% 构建用户列表
all_users = [user_types.URLLC, user_types.eMBB, user_types.mMTC];

% 为每个用户选择信道质量最好的基站
for i = 1:num_users
    % 找到信道质量最好的基站（路径损耗最小）
    [~, best_bs] = min(channel_matrix(i, :));
    user_assignment(i, best_bs) = 1;
end

% 验证约束：每个用户只能接入一个基站
for i = 1:num_users
    if sum(user_assignment(i, :)) ~= 1
        fprintf('警告：用户 %d 的接入决策不满足约束\n', i);
    end
end

end

function user_assignment = optimize_user_assignment_integrated(channel_matrix, user_types, slice_allocation, power_allocation, slice_params, phy_params)
%% 优化用户接入决策 - 基于当前QoS

% 初始化
num_users = size(channel_matrix, 1);
num_bs = size(channel_matrix, 2);
user_assignment = zeros(num_users, num_bs);

% 构建用户列表
all_users = [user_types.URLLC, user_types.eMBB, user_types.mMTC];

% 计算每个用户接入每个基站的预期QoS
qos_matrix = zeros(num_users, num_bs);

for i = 1:num_users
    user_name = all_users{i};

    for j = 1:num_bs
        % 计算该用户接入该基站的预期QoS
        qos_matrix(i, j) = calculate_user_qos_integrated(user_name, j, channel_matrix(i, j), ...
            slice_allocation(j, :), power_allocation(j), slice_params, phy_params);
    end
end

% 使用贪心算法优化用户接入
for i = 1:num_users
    % 找到该用户QoS最高的基站
    [~, best_bs] = max(qos_matrix(i, :));
    user_assignment(i, best_bs) = 1;

    % 更新其他用户的QoS（考虑资源竞争）
    qos_matrix = update_qos_matrix_integrated(qos_matrix, i, best_bs, user_types, slice_params);
end

% 验证约束
for i = 1:num_users
    if sum(user_assignment(i, :)) ~= 1
        fprintf('警告：用户 %d 的接入决策不满足约束\n', i);
    end
end

end

function qos_matrix = update_qos_matrix_integrated(qos_matrix, user_idx, bs_idx, user_types, slice_params)
%% 更新QoS矩阵（考虑资源竞争）

for i = 1:size(qos_matrix, 1)
    if i ~= user_idx && qos_matrix(i, bs_idx) > 0
        qos_matrix(i, bs_idx) = qos_matrix(i, bs_idx) * 0.95;  % 降低5%
    end
end

end

function slice_allocation = optimize_slice_allocation_integrated(user_assignment, user_types, bs_params, slice_params)
%% 优化切片资源分配

% 初始化
num_bs = 4;
num_slices = 3;
slice_allocation = zeros(num_bs, num_slices);

% 基站名称列表
bs_names = {'MBS_1', 'SBS_1', 'SBS_2', 'SBS_3'};

% 构建用户列表
all_users = [user_types.URLLC, user_types.eMBB, user_types.mMTC];

% 为每个基站分配切片资源
for j = 1:num_bs
    bs_name = bs_names{j};
    total_rbs = bs_params.(bs_name).rb_total;

    % 统计接入该基站的各类型用户数量
    urllc_users = 0;
    embb_users = 0;
    mmtc_users = 0;

    for i = 1:length(all_users)
        if user_assignment(i, j) == 1
            user_name = all_users{i};
            if startsWith(user_name, 'U')
                urllc_users = urllc_users + 1;
            elseif startsWith(user_name, 'e')
                embb_users = embb_users + 1;
            elseif startsWith(user_name, 'm')
                mmtc_users = mmtc_users + 1;
            end
        end
    end

    % 计算各切片所需资源块
    urllc_rbs = urllc_users * slice_params.URLLC.rb_per_user;
    embb_rbs = embb_users * slice_params.eMBB.rb_per_user;
    mmtc_rbs = mmtc_users * slice_params.mMTC.rb_per_user;

    % 检查资源是否足够
    total_required = urllc_rbs + embb_rbs + mmtc_rbs;

    if total_required <= total_rbs
        % 资源足够，按需分配
        slice_allocation(j, 1) = urllc_rbs;
        slice_allocation(j, 2) = embb_rbs;
        slice_allocation(j, 3) = mmtc_rbs;
    else
        % 资源不足，按优先级分配
        slice_allocation(j, 1) = min(urllc_rbs, total_rbs);
        remaining_rbs = total_rbs - slice_allocation(j, 1);

        slice_allocation(j, 2) = min(embb_rbs, remaining_rbs);
        remaining_rbs = remaining_rbs - slice_allocation(j, 2);

        slice_allocation(j, 3) = min(mmtc_rbs, remaining_rbs);
    end

    if sum(slice_allocation(j, :)) > total_rbs
        fprintf('警告：基站 %s 的资源分配超出限制\n', bs_name);
    end
end

end

function power_allocation = optimize_power_allocation_integrated(user_assignment, slice_allocation, channel_matrix, user_types, bs_params, slice_params, phy_params)
%% 优化功率分配

% 初始化
num_bs = 4;
power_allocation = zeros(num_bs, 1);

% 基站名称列表
bs_names = {'MBS_1', 'SBS_1', 'SBS_2', 'SBS_3'};

% 为每个基站优化功率
for j = 1:num_bs
    bs_name = bs_names{j};
    power_min = bs_params.(bs_name).power_min;
    power_max = bs_params.(bs_name).power_max;

    % 找到接入该基站的用户
    connected_users = find(user_assignment(:, j) == 1);

    if isempty(connected_users)
        % 没有用户接入，使用最小功率
        power_allocation(j) = power_min;
    else
        % 有用户接入，优化功率
        power_allocation(j) = optimize_bs_power_integrated(connected_users, j, channel_matrix, ...
            slice_allocation(j, :), power_min, power_max, user_types, slice_params, phy_params);
    end
end

end

function optimal_power = optimize_bs_power_integrated(connected_users, bs_idx, channel_matrix, slice_rbs, power_min, power_max, user_types, slice_params, phy_params)
%% 优化单个基站的功率

% 初始化搜索范围
left = power_min;
right = power_max;
tolerance = 0.1;  % 功率精度0.1dBm

% 构建用户列表
all_users = [user_types.URLLC, user_types.eMBB, user_types.mMTC];

while (right - left) > tolerance
    mid1 = left + (right - left) / 3;
    mid2 = right - (right - left) / 3;

    % 计算两个中点的QoS
    qos1 = calculate_bs_qos_integrated(connected_users, bs_idx, mid1, channel_matrix, slice_rbs, all_users, slice_params, phy_params);
    qos2 = calculate_bs_qos_integrated(connected_users, bs_idx, mid2, channel_matrix, slice_rbs, all_users, slice_params, phy_params);

    % 选择QoS更高的功率
    if qos1 > qos2
        right = mid2;
    else
        left = mid1;
    end
end

optimal_power = (left + right) / 2;

end

function total_qos = calculate_bs_qos_integrated(connected_users, bs_idx, power, channel_matrix, slice_rbs, all_users, slice_params, phy_params)
%% 计算基站的总QoS

total_qos = 0;

for i = 1:length(connected_users)
    user_idx = connected_users(i);
    user_name = all_users{user_idx};
    channel_loss = channel_matrix(user_idx, bs_idx);

    % 计算用户QoS
    qos = calculate_user_qos_integrated(user_name, bs_idx, channel_loss, slice_rbs, power, slice_params, phy_params);
    total_qos = total_qos + qos;
end

end

function qos = calculate_user_qos_integrated(user_name, bs_idx, channel_loss, slice_rbs, power, slice_params, phy_params)
%% 计算用户QoS

if startsWith(user_name, 'U')
    user_type = 'URLLC';
    slice_idx = 1;
elseif startsWith(user_name, 'e')
    user_type = 'eMBB';
    slice_idx = 2;
elseif startsWith(user_name, 'm')
    user_type = 'mMTC';
    slice_idx = 3;
end

params = slice_params.(user_type);

rate = calculate_transmission_rate_integrated(channel_loss, power, params.rb_per_user, phy_params);

delay = calculate_delay_integrated(params.rb_per_user, rate, params.rate_sla);

if delay <= params.delay_sla
    if strcmp(user_type, 'URLLC')
        qos = phy_params.alpha^delay;
    elseif strcmp(user_type, 'eMBB')
        if rate >= params.rate_sla
            qos = 1;
        else
            qos = rate / params.rate_sla;
        end
    elseif strcmp(user_type, 'mMTC')
        qos = 1;
    end
else
    qos = -params.penalty;
end

end

function rate = calculate_transmission_rate_integrated(channel_loss, power, rb_count, phy_params)
%% 计算传输速率

rx_power = 10^((power - channel_loss)/10);

noise_power = 10^((phy_params.noise_spectral_density + phy_params.noise_figure + 10*log10(phy_params.bandwidth))/10);

sinr = rx_power / noise_power;

rate = rb_count * phy_params.bandwidth * log2(1 + sinr);

end

function delay = calculate_delay_integrated(rb_count, rate, rate_sla)
%% 计算时延

transmission_delay = 0.1e-3;

queue_delay = rb_count * 0.01e-3;

delay = transmission_delay + queue_delay;

end

function total_qos = calculate_total_qos_integrated(user_assignment, slice_allocation, power_allocation, channel_matrix, user_types, slice_params, phy_params)
%% 计算系统总QoS

% 初始化
total_qos = 0;
num_users = size(user_assignment, 1);
num_bs = size(user_assignment, 2);

% 构建用户列表
all_users = [user_types.URLLC, user_types.eMBB, user_types.mMTC];

% 计算每个用户的QoS并求和
for i = 1:num_users
    user_name = all_users{i};

    % 找到用户接入的基站
    bs_idx = find(user_assignment(i, :) == 1);

    if ~isempty(bs_idx)
        % 计算用户QoS
        channel_loss = channel_matrix(i, bs_idx);
        slice_rbs = slice_allocation(bs_idx, :);
        power = power_allocation(bs_idx);

        qos = calculate_user_qos_integrated(user_name, bs_idx, channel_loss, slice_rbs, power, slice_params, phy_params);
        total_qos = total_qos + qos;
    end
end

end

function output_user_assignment_integrated(user_assignment, user_types)
%% 输出用户接入结果

fprintf('\n=== 用户接入决策结果 ===\n');

% 构建用户列表
all_users = [user_types.URLLC, user_types.eMBB, user_types.mMTC];
bs_names = {'MBS_1', 'SBS_1', 'SBS_2', 'SBS_3'};

% 统计各基站接入用户数量
for j = 1:4
    connected_users = find(user_assignment(:, j) == 1);
    fprintf('%s: %d个用户\n', bs_names{j}, length(connected_users));

    if ~isempty(connected_users)
        fprintf('  接入用户: ');
        for i = 1:length(connected_users)
            fprintf('%s ', all_users{connected_users(i)});
        end
        fprintf('\n');
    end
end

end

function output_slice_allocation_integrated(slice_allocation, bs_params)
%% 输出切片分配结果

fprintf('\n=== 切片资源分配结果 ===\n');

bs_names = {'MBS_1', 'SBS_1', 'SBS_2', 'SBS_3'};
slice_names = {'URLLC', 'eMBB', 'mMTC'};

fprintf('基站\t\tURLLC\t\teMBB\t\tmMTC\t\t总计\t\t限制\n');
fprintf('----\t\t-----\t\t----\t\t----\t\t----\t\t----\n');

for j = 1:4
    bs_name = bs_names{j};
    total_allocated = sum(slice_allocation(j, :));
    total_limit = bs_params.(bs_name).rb_total;

    fprintf('%s\t\t%d\t\t%d\t\t%d\t\t%d\t\t%d\n', ...
        bs_name, slice_allocation(j, 1), slice_allocation(j, 2), ...
        slice_allocation(j, 3), total_allocated, total_limit);
end

end

function output_power_allocation_integrated(power_allocation, bs_params)
%% 输出功率分配结果

fprintf('\n=== 功率分配结果 ===\n');

bs_names = {'MBS_1', 'SBS_1', 'SBS_2', 'SBS_3'};

fprintf('基站\t\t功率(dBm)\t\t范围(dBm)\n');
fprintf('----\t\t--------\t\t--------\n');

for j = 1:4
    bs_name = bs_names{j};
    power = power_allocation(j);
    power_min = bs_params.(bs_name).power_min;
    power_max = bs_params.(bs_name).power_max;

    fprintf('%s\t\t%.1f\t\t\t[%.1f, %.1f]\n', bs_name, power, power_min, power_max);
end

end

function plot_convergence_integrated(qos_history)
%% 绘制收敛曲线

figure;
plot(1:length(qos_history), qos_history, 'b-o', 'LineWidth', 2, 'MarkerSize', 6);
xlabel('迭代次数');
ylabel('总QoS');
title('算法收敛曲线');
grid on;

% 添加收敛信息
if length(qos_history) > 1
    improvement = (qos_history(end) - qos_history(1)) / qos_history(1) * 100;
    text(0.1*length(qos_history), 0.8*max(qos_history), ...
        sprintf('总改进: %.2f%%', improvement), 'FontSize', 12);
end

end
