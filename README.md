# Q4 异构网络资源管理优化问题

## 项目概述

本项目实现了异构网络环境下的资源管理优化算法，通过分层优化策略实现用户接入决策、切片资源分配和功率控制的联合优化，以最大化系统总 QoS。

## 环境要求

- MATLAB R2019b 或更高版本
- 统计和机器学习工具箱 (Statistics and Machine Learning Toolbox)

## 文件结构

```
Q4/
├── problem4_integrated.m               # 整合的主执行文件 (新增)
├── run_problem4.m                      # 原主运行脚本
├── problem4_solver.m                   # 原主求解程序
├── read_attachment4.m                  # 数据读取和预处理
├── build_channel_matrix.m              # 构建用户-基站信道矩阵
├── build_task_matrix.m                 # 构建任务到达矩阵
├── initial_user_assignment.m           # 初始用户接入决策
├── optimize_user_assignment.m          # 优化用户接入决策
├── optimize_slice_allocation.m         # 优化切片资源分配
├── optimize_power_allocation.m         # 优化功率分配
├── calculate_total_qos.m               # 计算系统总QoS
├── calculate_user_qos.m                # 计算用户QoS
├── output_user_assignment.m            # 输出用户接入结果
├── output_slice_allocation.m           # 输出切片分配结果
├── output_power_allocation.m           # 输出功率分配结果
├── plot_convergence.m                  # 绘制收敛曲线
├── README.md                           # 本文件
├── problem4_results.mat                # 求解结果文件
└── 附件/
    └── 附件4/
        ├── taskflow.xlsx               # 用户任务到达数据(赛题提供)
        ├── MBS_1.xlsx                  # 宏基站信道数据(赛题提供)
        ├── SBS_1.xlsx                  # 小基站1信道数据(赛题提供)
        ├── SBS_2.xlsx                  # 小基站2信道数据(赛题提供)
        ├── SBS_3.xlsx                  # 小基站3信道数据(赛题提供)
        └── readme.txt                  # 数据说明
```

## 快速开始

### 方法 1：使用整合文件（推荐）

```matlab
% 直接运行整合的主文件
problem4_integrated
```

### 方法 2：使用原始分离文件

```matlab
% 运行原始的主脚本
run_problem4
```

## 算法说明

### 优化策略

算法采用分层优化策略，包含三个层次：

1. **用户接入决策**：基于信道质量为用户选择最优基站
2. **切片资源分配**：根据用户类型和需求分配资源块
3. **功率控制**：优化各基站的发射功率

### 用户类型

- **URLLC 用户** (U1-U10)：超可靠低时延通信，要求低时延高可靠性
- **eMBB 用户** (e1-e20)：增强移动宽带，要求高数据速率
- **mMTC 用户** (m1-m40)：大规模机器类通信，要求低功耗

### 网络拓扑

- **宏基站 MBS_1**：位于(0,0)，总资源块 100 个
- **小基站 SBS_1**：位于(0,500)，总资源块 50 个
- **小基站 SBS_2**：位于(-433,-250)，总资源块 50 个
- **小基站 SBS_3**：位于(433,-250)，总资源块 50 个

## 输出结果

程序运行后将生成：

1. **problem4_results.mat**：包含所有优化结果的 MAT 文件
2. **收敛曲线图**：显示算法迭代过程中 QoS 的变化
3. **控制台输出**：详细的优化过程和最终结果

## 主要功能模块

- **数据读取与预处理**：从 Excel 文件读取信道和任务数据
- **信道矩阵构建**：建立用户-基站信道损耗矩阵
- **任务矩阵构建**：处理用户任务到达模式
- **迭代优化算法**：交替优化三个决策变量直至收敛
- **QoS 计算**：根据不同用户类型计算服务质量
- **结果可视化**：绘制收敛曲线和输出详细结果

## 注意事项

1. 确保附件文件夹中包含所有必需的 Excel 数据文件
2. 算法可能需要几分钟时间完成，请耐心等待
3. 如遇到内存不足问题，可适当减少用户数量或迭代次数
