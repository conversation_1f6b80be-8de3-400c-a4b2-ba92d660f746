%% 测试整合版本的MATLAB文件
% 这个脚本用于测试problem4_integrated.m是否能正常运行

clear; clc; close all;

fprintf('========================================\n');
fprintf('    测试整合版本的MATLAB文件\n');
fprintf('========================================\n\n');

% 检查数据文件是否存在
data_path = '附件/附件4/';
required_files = {'taskflow.xlsx', 'MBS_1.xlsx', 'SBS_1.xlsx', 'SBS_2.xlsx', 'SBS_3.xlsx'};

fprintf('检查数据文件是否存在...\n');
all_files_exist = true;

for i = 1:length(required_files)
    file_path = [data_path, required_files{i}];
    if exist(file_path, 'file')
        fprintf('✓ %s 存在\n', required_files{i});
    else
        fprintf('✗ %s 不存在\n', required_files{i});
        all_files_exist = false;
    end
end

if all_files_exist
    fprintf('\n所有数据文件都存在，可以运行整合版本。\n');
    fprintf('请运行以下命令：\n');
    fprintf('>> problem4_integrated\n\n');
    
    % 询问是否立即运行
    user_input = input('是否立即运行整合版本？(y/n): ', 's');
    if strcmpi(user_input, 'y') || strcmpi(user_input, 'yes')
        fprintf('\n开始运行整合版本...\n');
        problem4_integrated;
    else
        fprintf('测试完成。您可以稍后手动运行 problem4_integrated\n');
    end
else
    fprintf('\n错误：缺少必要的数据文件。\n');
    fprintf('请确保以下文件存在于 %s 目录中：\n', data_path);
    for i = 1:length(required_files)
        fprintf('  - %s\n', required_files{i});
    end
end

fprintf('\n========================================\n');
fprintf('    测试完成\n');
fprintf('========================================\n');
